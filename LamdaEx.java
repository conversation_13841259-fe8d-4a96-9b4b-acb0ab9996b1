import java.util.Scanner;

interface Aop{
     double operation(double a,double b);
}//functional interface
class LamdaEx {
    public static void main(String[] args) {
        Scanner input=new Scanner(System.in);
        System.out.println("enter two values:");
        double x1=Double.parseDouble(input.nextLine());
        double x2=Double.parseDouble(input.nextLine());
        //lamda expressions
        Aop ao=(double a,double b)->(a+b);
        System.out.println("--------------------------------");
        System.out.println("Sum is :"+ao.operation(x1,x2));
        System.out.println("-------------------------------");
        ao=(double a,double b)->(b-a);
        System.out.println("Diff is :"+ao.operation(x1,x2));
        System.out.println("--------------------------------");
        ao=(double a,double b)->(a*b);
        System.out.println("Product is :"+ao.operation(x1,x2));
        System.out.println("--------------------------------");
        ao=(double a,double b)->(a/b);
        System.out.println("Division is :"+ao.operation(x1,x2));
        //closing the scanner obj.
        input.close();
    }
    
}
