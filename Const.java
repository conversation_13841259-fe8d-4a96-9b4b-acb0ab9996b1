// Const.java
class Const {
    int a,b;
    Const(){
        a=3;
        b=4;
        System.out.println("value of a="+a);
        System.out.println("value of b="+b);
    }
    Const(int x){
        a=x;
        b=x;
        System.out.println("value of a="+a);
        System.out.println("value of b="+b);
    }
    //void display(){
       // System.out.println("value of a="+a);
       // System.out.println("value of b="+b);
    //}
}
// ConstDemo.java
class ConstDemo{
public static void main(String[] args) {
    System.out.println("---------------------");
    Const c1=new Const(10);
    //c1.display(); 
    System.out.println("---------------------");
    Const c2=new Const();
    //c2.display(); 
    System.out.println("--------------------");
}
}
