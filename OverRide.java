import java.util.Scanner;
class Circle
{
    Scanner sc2=new Scanner(System.in);
    final float pi=3.14f;
    void area(){
        System.out.println("Enter radius:");
        float r=Float.parseFloat(sc2.nextLine());
        System.out.println("area of circle :"+(pi*r*r));
        System.out.println("------------------------");
    }
}
class Rect extends Circle
{
 void area(){
    System.out.println("------------------------");
    System.out.println("Enter length:");
    double l=Double.parseDouble(sc2.nextLine());
    System.out.println("Enter breadth:");
    double b=Double.parseDouble(sc2.nextLine());
    System.out.println("area of rectangle:"+(l*b));
    System.out.println("------------------------");
    super.area();
 }
}
class Square extends Rect
{
void  area(){
    System.out.println("------------------------");
    System.out.println("Enter side:");
    double sc2=Double.parseDouble(super.sc2.nextLine());
    System.out.println("area of square :"+(sc2*sc2)); 
    System.out.println("------------------------");
    super.area();
}
}
public class OverRide {

    public static void main(String[] args) {
        Square s=new Square();
        s.area();
    }
}