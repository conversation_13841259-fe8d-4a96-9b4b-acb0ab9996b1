//Intface2.java
interface I1 
{
    void f1();//public static void by default
}
interface I2 extends I1
{
    void f2();//public static void by default
}
class C2 implements I2
{
    public void f1()
    {
        System.out.println("f1 is overridden");
    }
    public void f2()
    {
        System.out.println("f2 is overridden");
    }
    void f3()
    {
       System.out.println("f3 is special"); 
    }
}
class  Intface2 
{
    public static void main(String[] args)
     {
        C1 c=new C1();
        System.out.println("============================");
        c.f1();
        c.f2();
        c.f3();
        System.out.println("ref of i2:"+c);
        System.out.println("ref of i2:"+c.hashCode());
        System.out.println("============================");
        I1 i=new C1();
        i.f1();
        System.out.println("ref of i2:"+i);
        System.out.println("ref of i2:"+i.hashCode());
        System.out.println("============================");
        I2 i2=new C1();
        i2.f1();
        i2.f2();
        System.out.println("ref of i2:"+i2);
        System.out.println("ref of i2:"+i2.hashCode());
        System.out.println("============================");
    }
}
