//Details.java
import java.util.Scanner;
class Details{
    int stdno;
    String std_name,cllg_name;
    float marks;
    void getDetails()
    {
        Scanner sc=new Scanner(System.in);
        System.out.println("Enter student id:");
        stdno=Integer.parseInt(sc.nextLine());
        System.out.println("Enter student name:");
        std_name=sc.nextLine();
        System.out.println("Enter student Marks:");
        marks=Float.parseFloat(sc.nextLine()); 
        System.out.println("Enter college name:");
        cllg_name=sc.nextLine();
    } 
    void display(){
         System.out.println("----------------------------------");
         System.out.println("Student Details");
         System.out.println("----------------------------------");
         System.out.println("student id:"+stdno);
         System.out.println("student name:"+std_name);
         System.out.println("student Marks:"+marks);
         System.out.println("college name:"+cllg_name);
         System.out.println("----------------------------------");
    }//Details--BLC
}
public class Student {
    public static void main(String[] args) {
        Details d=new Details();
        d.getDetails();
        d.display();
    }
}//Student---ELC
