//NumGenDemo.java
import java.util.Scanner;
class Th1 extends Thread{
    public void run()
    {
      Scanner s=new Scanner(System.in);
      int count=Thread.activeCount();
      System.out.println("=====================================");
      System.out.println("No.of active Threads in Run():"+count);
      System.out.println("=====================================");
      System.out.println("enter a number:");
      int n=Integer.parseInt(s.nextLine());
      if (n<=0)
      {
        System.out.println("invalid input...!");
      }
      else
      {
        System.out.println("============================");
        System.out.println("Numbers within :"+n);
        System.out.println("============================");
        try
        {
             for(int i=1;i<=n;i++)
            {
            System.out.println("value of i is: "+i);
            Thread.sleep(1000);
            } 
        }catch(InterruptedException ie)
        {
        System.out.println("problem in ThreadException....!");
        }
        System.out.println("============================");
      }//else 
      s.close();  
    }//run()
}//business logic class
class NumGenDemo {
    public static void main(String[] args) {
        try {
            Thread.sleep(5000);
        } catch (InterruptedException ie) {
           System.out.println("problem in making Thread  to sleep....!");
        } 
        System.out.println("============================");
        int count1=Thread.activeCount();
        System.out.println("No.of active Threads in main():"+count1);
        System.out.println("============================");
        Th1 t=new Th1();
        t.start();
    }
}
