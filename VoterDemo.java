import java.util.Scanner;

class Voter{
    String Voter_name;
    int age;
    void decide()
    {
     Scanner sc=new Scanner(System.in);
     System.out.println("Enter voter name:");
     Voter_name=sc.nextLine();
      while (true)
       {
        System.out.println("Enter your Age:");
        age=Integer.parseInt(sc.nextLine());
        if (age>=18) 
                break;
        else
        System.out.println(Voter_name+" is not eligible to vote");    
      }
      System.out.println(Voter_name+" is eligible to Vote"); 
    }
}
public class VoterDemo {
    public static void main(String[] args) {
        Voter v=new Voter();
        v.decide();
    }
}// VoterDemo---ELC
