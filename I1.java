public interface I1 {
    void f1();
}
interface I2{
    void f2();
}
class C1 implements I1,I2{
    public void f1(){
        System.out.println("f1 is overridden");
    }
    public void f2(){
        System.out.println("f2 is overridden");
    }
    void f3(){
       System.out.println("f3 is special"); 
    }
}
class  Intface1 {
    public static void main(String[] args) {
        C1 c=new C1();
        c.f1();
        c.f2();
        c.f3();
        System.out.println("============================");
        I1 i=new C1();
        i.f1();
        System.out.println("============================");
        I2 i2=new C1();
        i2.f2();
        System.out.println("============================");
    }
}
