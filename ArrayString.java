import java.util.Arrays;
import java.util.Scanner;

class ArrayString 
{
    Scanner s1=new Scanner(System.in);
    int m=s1.nextInt();
    String[] array1=new String[m];
    void displayVal(){
        
    for(int i=0;i<array1.length;i++)
    {
        array1[i]=s1.nextLine();
    }
    Arrays.sort(array1);
    for(int j=0;j<array1.length;j++)
    {
        System.out.println(array1[j]);
    }
}

}
class ArrayStringDemo{
    public static void main(String[] args) {
        ArrayString as=new ArrayString();
        as.displayVal();
    }
}
