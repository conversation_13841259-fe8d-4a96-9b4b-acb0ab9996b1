class Hp{
    int stno;
    String name;
    void getLap(int stno,String name)
    {
    this.stno=stno;
    this.name=name;
    }
    void display()
    {
        System.out.println("val of num="+stno);
        System.out.println("val of name="+name);
    }
}//hp----BLC

//FactoryDemo.java
 class FactoryDemo {
    public static void main(String[] args) {
        System.out.println("------------------------------------");
        System.out.println("***** EXAMPLE OF DEEP COPY *****");
        Hp h1=new Hp();
        h1.getLap(207, "mani");
        System.out.println("------------------------------------");
        h1.display();
        System.out.println("hash val of h1="+h1.hashCode());
        Hp h2=h1;
        System.out.println("----------------------");
        h2.getLap(208,"manideep");
        h2.display();
        System.out.println("hash val of h2="+h2.hashCode());
        System.out.println("----------------------");
        h1.display();
        System.out.println("hash val of h1="+h1.hashCode());
        System.out.println("----------------------");
    }
}// ----ELC
