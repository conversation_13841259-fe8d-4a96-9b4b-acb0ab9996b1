//Test.java
class Test{
    int x;
    String name;
    Test(){
        System.out.println("--------------------");
        x=10;
        System.out.println("value of x="+x);
    }
    Test(int a){
        System.out.println("--------------------");
        x=a;
        System.out.println("value of x="+x);
    }
    Test (String m){
        System.out.println("--------------------");
        name=m;
        System.out.println("my name is:"+name);
    }
}
//OverloadConst class-Elc
class OverloadConst {
    public static void main(String[] args) {
        Test t1=new Test();
        System.out.println("address of t1="+t1);
        System.out.println("address of t1="+t1.hashCode());
        System.out.println("--------------------");
        Test t2=new Test(100);
        System.out.println("address of t2="+t2);
        System.out.println("address of t2="+t2.hashCode());
        System.out.println("--------------------");
        Test t3=new Test("Manohar");
        System.out.println("address of t3="+t3);
        System.out.println("address of t3="+t3.hashCode());
        System.out.println("--------------------");
    }
}
