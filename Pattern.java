import java.util.Scanner;
public class Pattern
{
       public static void main(String[] args)
        {
        System.out.println("enter no.of symbols");
        Scanner n= new Scanner(System.in);
        int m=n.nextInt();
        /*for(int i=1;i<=m;i++){
            for(int j=1;j<=m-i;j++){
                System.out.print(" ");
            }
            for(int k=1;k<=i;k++){
                System.out.print("* ");
            }
            System.out.println();
        }*/
       /*  for(int i=1;i<=m;i++){
            for(int j=1;j<=i;j++){
                System.out.print(i);
            }
            System.out.print("\n");
        }*/
        /*for(int i=1;i<=m;i++){
            for(int j=1;j<=i;j++){
                System.out.print(j);
            }
            System.out.print("\n");
        }*/
        /*int a=1;
        for(int i=1;i<=m;i++)
        {
            for(int j=1;j<=i;j++){
                System.out.print(a+" ");
                a++;
            }
            System.out.print("\n");
            n.close();
        }*/
        /*for (int i=0;i<m;i++){
            for(int j=m-i;j>=1;j--){
                System.out.print(" *");
            }
            System.out.println();
        }*/
        /*for(int i=1;i<=m;i++)
        {
            int k=m-1,c=i;
            for(int j=1;j<=i;j++){
                System.out.print(c+" ");
                c=c+k;
                k--;
            }
            System.out.println();
        }*/
        /*for(int i=1;i<=m;i++)
        {
            for(int j=1;j<=m-i;j++)
            {
                System.out.print(" ");
            }
            for(int k=1;k<=(2*i-1);k++)
            {
                System.out.print("*");
            }
            System.out.println();
        }*/
                for (int i = 1; i <= m; i++) 
                {
                    for (int j = 1; j <= m; j++) 
                    {
                        if (i == 1 || i == m || j == 1 || j == m)
                        {
                            System.out.print("*");
                        } else 
                        {
                            System.out.print(" ");
                        }
                    }
                    // Move to the next line after each row
                    System.out.println();
                }
                n.close();
        }
}
        
    
