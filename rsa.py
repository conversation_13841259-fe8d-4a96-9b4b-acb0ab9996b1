from Crypto.PublicKey import RSA
from Crypto.Cipher import P<PERSON>CS1_OAEP
from Crypto.Random import get_random_bytes

def rsa_demo():
    print("=== RSA Encryption Demo ===")

    # Generate RSA key pair
    key = RSA.generate(2048)
    private_key = key.export_key()
    public_key = key.publickey().export_key()

    print("\nPrivate Key:")
    print(private_key.decode())

    print("\nPublic Key:")
    print(public_key.decode())

    # Message to encrypt
    plaintext = b"name is nani"
    print("\nOriginal Message:", plaintext.decode())

    # Encrypt with the public key
    public_key_obj = RSA.import_key(public_key)
    cipher = PKCS1_OAEP.new(public_key_obj)
    ciphertext = cipher.encrypt(plaintext)
    print("\nCiphertext:", ciphertext)

    # Decrypt with the private key
    private_key_obj = RSA.import_key(private_key)
    decipher = PKCS1_OAEP.new(private_key_obj)
    decrypted_text = decipher.decrypt(ciphertext)
    print("\nDecrypted Message:", decrypted_text.decode())

# Run the RSA demo
rsa_demo()
