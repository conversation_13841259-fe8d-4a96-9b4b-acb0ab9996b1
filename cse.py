from Crypto.Cipher import AES, DES
from Crypto.Util.Padding import pad, unpad
from Crypto.Random import get_random_bytes

# Function to encrypt and decrypt using AES
def aes_encryption_demo():
    print("=== AES Encryption Demo ===")
    key = get_random_bytes(16)  # AES key must be 16, 24, or 32 bytes
    iv = get_random_bytes(16)   # Initialization vector
    plaintext = b"my name is mani"
    # Encrypt
    cipher = AES.new(key, AES.MODE_CBC, iv)
    ciphertext = cipher.encrypt(pad(plaintext, AES.block_size))
    print("Ciphertext (AES):", ciphertext)
    # Decrypt
    decipher = AES.new(key, AES.MODE_CBC, iv)
    decrypted_text = unpad(decipher.decrypt(ciphertext), AES.block_size)
    print("Decrypted text (AES):", decrypted_text.decode())

# Function to encrypt and decrypt using DES
def des_encryption_demo():
    print("\n=== DES Encryption Demo ===")
    key = get_random_bytes(8)  # DES key must be 8 bytes
    iv = get_random_bytes(8)   # Initialization vector
    plaintext = b"your name is nani"
    # Encrypt
    cipher = DES.new(key, DES.MODE_CBC, iv)
    ciphertext = cipher.encrypt(pad(plaintext, DES.block_size))
    print("Ciphertext (DES):", ciphertext)
    # Decrypt
    decipher = DES.new(key, DES.MODE_CBC, iv)
    decrypted_text = unpad(decipher.decrypt(ciphertext), DES.block_size)
    print("Decrypted text (DES):", decrypted_text.decode())

# Run demos
aes_encryption_demo()
des_encryption_demo()
