class EvenNumbers extends Thread
{
 public void run()
 {
    try
    {
        for (int i = 2; i <=10 ; i=i+2) 
        {
        System.out.println("Even Numbers are :"+i);
        Thread.sleep(1000);
        }
    }catch(InterruptedException ie)
    {
        System.out.println("pblm in Thread Exeception");
    }
 }
}//EvenNumbers

class OddNumbers implements Runnable
{
    public void run()
 {
    try
    {
        for (int i = 1; i <=10 ; i=i+2) 
        {
        System.out.println("odd Numbers are :"+i);
        Thread.sleep(1000);
        }
    }catch(InterruptedException ie)
    {
        System.out.println("pblm in Thread Exeception");
    }
 }
}//OddNumbers

class MultiThreads
 {
    public static void main(String[] args) {
        OddNumbers on=new OddNumbers();
        EvenNumbers en=new EvenNumbers();
        Thread t=new Thread(on);
        t.start();
        en.start();
    }
}//MultiThreads
