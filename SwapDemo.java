class Swap {
    int a,b;
    void setValues(){
        a=10;
        b=20;
    }
    void swap(){
        a=a+b;
        b=a-b;
        a=a-b;
    }
    void display(){
        System.out.println("the value of a="+a);
        System.out.println("the value of b="+b);
    }
    
}
class SwapDemo {
    public static void main(String[] args) {
        Swap s=new Swap();
            s.setValues();
                System.out.println("orginal values");
                System.out.println("-----------------");
            s.display();
            System.out.println("-----------------");
            s.swap();
                 System.out.println("swapped values");
                 System.out.println("-----------------");
            s.display();
    }

    
}
