import java.util.Scanner;

class Demo
{
int a=0,b=1,c,num;
void set()
    { 
        System.out.println("enter no.of terms need:");
        Scanner n=new Scanner(System.in);
        num=n.nextInt();
        n.close();
    }
void cal()
{
    for(int i=0;i<=num;i++)
     {
        System.out.print(a+" ");
        c=a+b;
        a=b;
        b=c;
     }
}
}
public class Fibnonacci 
{
    public static void main(String[] args) {
        Demo d=new Demo();
        d.set();
        d.cal();
    }
}
