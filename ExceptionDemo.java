import java.util.Scanner;
import mt.Multable2;
import p1.Exception1;
class ExceptionDemo {
    public static void main(String[] args) 
    {
       Scanner s=new Scanner(System.in);
       System.out.println("enter value:");
       String val=s.nextLine();
       try
       {
        Multable2 m=new Multable2();
        m.table(val);
       }catch(Exception1 obj)
       {
            System.out.println("Do not enter -ve values (or) zero as input..!!");
       }
       catch(NumberFormatException nfe)
       {
        System.out.println("Do not enter 'Special sysmbols/alphanumbers/Strings' as input");
       }
    }
}
