 public class Encap {
        private int acc_No;
        String name;
        private float pin;
        String Branch;
        String District;
        Encap(){
            acc_No=1234;
            name="mani";
            pin=0409f;
            Branch="ponnaluru";
            District="prakasam";
        }
        
        void getValues(int acc_No,String name,int pin,String Branch,String District)
        {
            this.acc_No=acc_No;
            this.name=name;
            this.pin=pin;
            this.Branch=Branch;
            this.District=District;
        }
        void display()
        {
            System.out.println("acc_no "+acc_No);
            System.out.println("name "+name);
            System.out.println("pin "+pin);
            System.out.println("Branch "+Branch);
            System.out.println("district "+District);
        }
}
