//Atm_Demo.java 
import java.util.Scanner;
import bank.AtmOperations;
import bank.Atm_menu;
import bank.DepositException;
import bank.WithdrawException;
import bank.InsufficientException;
class Atm_Demo
{
    public static void main(String[] args)
    {
        Scanner s=new Scanner(System.in);
        AtmOperations ao=new AtmOperations();
        while (true) 
        {
            try
            {
                Atm_menu.menu();
                System.out.println("Enter your choice:");
                int ch=Integer.parseInt(s.nextLine());
                switch (ch) 
                {
                 case 1:
                 try {
                    ao.deposit();
                    } catch (NumberFormatException nfe)
                    {
                        System.out.println("Don't deposit String/AlphaNumeric/Special Symbols as ur choice...!");
                    }
                    catch(DepositException obj2){
                        System.out.println("Don't try to deposit negative/zero values:");   
                    }
                    break;
                case 2:
                try {
                    ao.withdraw();
                } catch (NumberFormatException nfe) {
                    System.out.println("Don't try to withdarw String/AlphaNumeric/Special Symbols as ur choice...!");
                }
                catch(WithdrawException w){
                    System.out.println("Don't try to withdraw  negative/zero values:");
                }catch(InsufficientException ins){
                    System.out.println("Don't try to withdraw insufficient amt..!");
                }
                    break;
                case 3:ao.balenquiry();
                    break;
                case 4:System.out.println("Thanks you for visiting");
                    System.exit(0);
                default:System.out.println("Please enter valid choice---TRY AGAIN..!");
                break;
                }
            }catch(NumberFormatException nfe)
            {
                System.out.println("Don't Enter String/AlphaNumeric/Special Symbols as ur choice...!");
            }
        } 
    }
}
