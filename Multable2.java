
package mt;
import p1.Exception1;
public class Multable2 {
   public void table(String s) throws Exception1,NumberFormatException
    {
        int s1=Integer.parseInt(s);
        if( s1<=0)
        {
            Exception1 obj =new Exception1();
            throw obj;
        }//if-block
        else
        {
            System.out.println("===================");
            System.out.println("multiplication table:");
            System.out.println("===================");
            for(int i=1;i<=s1;i++)
            {
                System.out.println("\t"+s1+"x"+i+"="+(s1*i));
            }
            System.out.println("===================");
        }//else-block
    }//method-end
}//class-end
