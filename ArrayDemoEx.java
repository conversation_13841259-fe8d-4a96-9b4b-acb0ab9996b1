import java.util.Arrays;
import java.util.Scanner;

class ArrayDemoEx {
    Scanner s=new Scanner(System.in);
    
    void getInputs(){
        System.out.println("enter array size:.....!");
    int n=s.nextInt();
        int[] array=new int[n];
        System.out.println("================================");
        for(int i=0;i<array.length;i++)
        {
            array[i]=s.nextInt();
        }
        s.close();
        Arrays.sort(array);
        System.out.println("================================");
        for(int j=0;j<array.length;j++)
        {
            System.out.println(array[j]);
        }
        System.out.println("================================");
    }
}
class ArrayDemo{
public static void main(String[] args) {
    ArrayDemoEx ad=new ArrayDemoEx();
    ad.getInputs();
}
}