//Inher.java
import java.util.Scanner;
class University {
    String Uni_name,Uni_loc;
    Scanner sc2=new Scanner(System.in);
    void getDetails1()
    {
        System.out.println("Enter University name:");
        Uni_name=sc2.nextLine();
        System.out.println("Enter University location:");
        Uni_loc=sc2.nextLine();
        display1();
    }
    void display1()
    {
        System.out.println("-------------------------------");
        System.out.println("U N I V E R S I T Y  D E T A I L S");
        System.out.println("-------------------------------");
        System.out.println("University name:"+Uni_name);
        System.out.println("University location:"+Uni_loc);
        System.out.println("-------------------------------");
    }

}//University-----BASE CLASS
class College extends University
{
String Cllg_name,Cllg_loc;
    void getDetails2()
    {
        System.out.println("Enter cllg name:");
        Cllg_name=sc2.nextLine();
        System.out.println("Enter cllg location:");
        Cllg_loc=sc2.nextLine();
        display2();
    }
    void display2()
    {
        System.out.println("-------------------------------");
        System.out.println("C O L L E G E  D E T A I L S");
        System.out.println("-------------------------------");
        System.out.println("cllg:"+Cllg_name);
        System.out.println("cllg:"+Cllg_loc);
        System.out.println("-------------------------------");
    }
}//College---INTERMEDIATE BASE CLASS
class Student extends College
{   
    int Roll_no;
    String std_name,crs;
    void getStdDetails3()
    {
        System.out.println("Enter std roll_no:");
        Roll_no=Integer.parseInt(sc2.nextLine());
        System.out.println("Enter std name:");
        std_name=sc2.nextLine();
        System.out.println("Enter crs name:");
        crs=sc2.nextLine();
        display3();
    }
    void display3()
    {
        System.out.println("-------------------------------");
        System.out.println("S T U D E N T D E T A I L S");
        System.out.println("-------------------------------");
        System.out.println("std_rollno:"+Roll_no);
        System.out.println("Std_name:"+std_name);
        System.out.println("crs_name:"+crs);
        System.out.println("-------------------------------");
    }
}// SUB CLASS
class Inher{
    public static void main(String[] args) {
        Student s=new Student();
        s.getDetails1();
        s.getDetails2();
        s.getStdDetails3();
    }
}
