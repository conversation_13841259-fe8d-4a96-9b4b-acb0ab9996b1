import java.util.Scanner;
class Price{
    double discount,l_price,D_rate;
    void SetValues()
    {
        Scanner s1=new Scanner(System.in);
        System.out.println("enter the Listed Price:");
        l_price=s1.nextDouble();
        System.out.println("enter the Discount Rate:");
        D_rate=s1.nextDouble();
    }
    void cal(){
    discount=l_price*(D_rate/100);
    System.out.println("---------------------------------");
    System.err.println("discount of the item:"+discount);
    System.out.println("the cost of item :"+(l_price-discount));
    }
}
public class Discount {
    public static void main(String[] args) {
        Price p=new Price();
        p.SetValues();
        p.cal();
    }
}
