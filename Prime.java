import java.util.Scanner;

class Prime {
    int num;
    void setValue()
    {
        System.out.println("enter a number");
    Scanner n=new Scanner(System.in);
    num=Integer.parseInt(n.nextLine());
    }
    void decide(){
        boolean isprime=true;
        for(int i=2;i<num;i++)
        {
            if(num%i==0)
            {
                isprime=false;
            System.out.println("Not a prime");
            break;
            }   
        }
        if (isprime){
            System.out.println(num+" is a prime");
        }
    }
}//Prime.java---BLC
class PrimeEx{
    public static void main(String[] args) {
        Prime p=new Prime();
        p.setValue();
        p.decide();
    }
}
