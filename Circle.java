class Circle {
   final static float pi=3.14f;
    //static float area(float r)
    //{
     //float ar=pi*r*r;
     //return ar;
   // }
    //public static void main(String[] args) {
        //float res=area(5.5f);
        //System.out.println("the area of circle="+res);
   // }
   double rad,ac,pc;
   void setValues(double r){
    rad=r;
   }
   void cal(){
    ac=pi*rad*rad;
    pc=2*pi*rad;
   }
   void display(){
    System.out.println("radius of circle="+rad);
    System.out.println("Area of circle="+ac);
    System.out.println("perimeter of a circle="+pc);
   }
}
class CicleDemo
{
    public static void main(String mg[]) {
        double d = 0.0; // Declare d outside the loop
        for(String a:mg)
        {
            // Check if there are any command-line arguments
            if (mg.length > 0) 
            {
                d = Double.parseDouble(mg[0]); // Assign the first argument to d
            } 
            else
            {
                System.out.println("No radius provided. Using default value 0.0.");
            }
        }
        Circle c1=new Circle();
            c1.setValues(d);
            c1.cal();
            c1.display();
    }
    

}
