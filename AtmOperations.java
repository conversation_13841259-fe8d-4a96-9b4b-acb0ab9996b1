package bank;
import java.util.Scanner;
public class AtmOperations 
{
    Scanner s=new Scanner(System.in);
     double bal;
     public AtmOperations()
     {
        bal=500.00;
     } 
     public void deposit()  throws NumberFormatException
     {
        System.out.println("Enter the amount to deposit:");
        double damt=Double.parseDouble(s.nextLine());
        if(damt<=0){
            DepositException obj2=new DepositException();
            throw obj2;
        }else
        {
            bal +=damt;
            System.out.println("Amt credited:"+damt);
            System.out.println("balance:"+bal);
        }

     }
    public void withdraw() throws WithdrawException,NumberFormatException
    {
        System.out.println("Enter amt to be withdraw:");
        double Wamt=Double.parseDouble(s.nextLine());
        if(Wamt<=0){
            WithdrawException w=new WithdrawException();
            throw w;
        }
        if(Wamt>bal){
            InsufficientException ins=new InsufficientException();
            throw ins;
        }
        else{
            if(Wamt<=bal){
                bal -= Wamt;
                System.out.println("amt is Debited:"+Wamt);
                System.out.println("balance after amt withdrawn:"+bal);
            }
        }
     }
     public void balenquiry()
     {
        System.out.println("Balance Amt Available:"+bal);
     }
}
