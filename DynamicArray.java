/*class Student1{
    int R_no;
    String name;
    Student1(int R_no,String name){
        this.R_no= R_no;
        this.name= name;
    }
}
class Array{
    public static void main(String[] args) {
        Student1[] array;
        array=new Student1[3];
        array[0]=new Student1(1,"mani");
        array[1]=new Student1(2,"sai");
        array[2]=new Student1(3,"abhi");

        for(int i=0;i<array.length;i++){
            System.out.println("Element at "+ i + ": {"+ array[i].R_no +" " + array[i].name +"}");
        }
    }
}*/

import java.util.Arrays;
import java.util.Scanner;

 public class DynamicArray
{
    private int[] array;
    private  int size,d;
    public void setSize(int size)
    {
        this.size=size;
        array=new int[size];
    }
    public void arrayDefine()
    {
        System.out.println("Enter "+size+" array values");
        Scanner scanner=new Scanner(System.in);
        for(int i=0;i<size;i++)
        {
            array[i]=scanner.nextInt();
        }
        scanner.close();
        Arrays.sort(array);
        d=Arrays.binarySearch(array, 45);
    }
    public void displayArray()
    {
        System.out.println("array values are:");
        for(int j=0;j<array.length;j++)
        {
            System.out.print(array[j]+"\t");
        }
        System.out.println();
        System.out.println("===================================");
        System.out.println("key value found at index of "+d+" in the array");
    }
}