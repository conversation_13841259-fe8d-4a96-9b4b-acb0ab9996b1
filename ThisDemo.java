class Test{
    int a,b;
    Test (){
        System.out.println("the value of a:"+a);
        System.out.println("the value of b:"+b);
    }
    Test(int a,int b){
         this.a=a;
         this.b=b;
    }
    void display(){
        System.out.println("the value of a="+a);
        System.out.println("the value of b="+b);
    }
}
//ThisDemo.java
class ThisDemo
{
public static void main(String m[]) 
{
    Test t1= new Test();
    System.out.println("--------------------------");
    Test t2= new Test(10,20);
    t2.display();
    System.out.println("--------------------------");
}
}
